/*
 * SPDX-FileCopyrightText: 2021-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include "ble_gatt.h"
#include "dev_id.h"
#include "version.h"
#include "data_collector.h"

#define TAG "GATT"

// 全局功能表
static uint16_t slave_handle_table[HRS_IDX_NB];
//
// 应用实例表（全局），用于管理所有注册到 BLE 协议栈的 GATT 应用实例
// 每个实例结构体包含：
// 回调函数 gatts_cb：处理属于该实例的所有事件
// 接口句柄 gatts_if：协议栈分配的 GATT 接口 ID，用于事件分发和调用相关 API
static struct gatts_profile_inst gatt_app_profile_table[PROFILE_NUM] = {
    [SLAVE_PROFILE_IDX] = {
        .gatts_cb = gatts_slave_profile_event_handler,
        .gatts_if = ESP_GATT_IF_NONE, // 初始为空，待注册成功后由协议栈赋值
    },
};

/* Service */
static const uint16_t GATTS_SERVICE_UUID_SENSOR = 0x00FF;
static const uint16_t GATTS_CHAR_UUID_JAR_INFO = 0xFF01;
static const uint16_t GATTS_CHAR_UUID_JAR_SAMPLE_DATA = 0xFF02;
static const uint16_t GATTS_CHAR_UUID_JAR_SAMPLE_RATE = 0xFF03;
//
static const uint16_t primary_service_uuid = ESP_GATT_UUID_PRI_SERVICE;
static const uint16_t character_declaration_uuid = ESP_GATT_UUID_CHAR_DECLARE;
static const uint16_t character_client_config_uuid = ESP_GATT_UUID_CHAR_CLIENT_CONFIG;
static const uint8_t char_prop_only_read = ESP_GATT_CHAR_PROP_BIT_READ;
static const uint8_t char_prop_read_write = ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_READ;
static const uint8_t char_prop_read_notify = ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_NOTIFY;

//
static uint8_t sample_rate = 1; // 默认采集频率1Hz
static uint16_t notify_conn_id = 0;
static esp_gatt_if_t notify_gatts_if = 0;
static bool notify_enable = false;
//
static struct gatts_jar_info jar_info = {
    .device_id = 0,
    .device_mac = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    .soft_ver = 0x00,
    .battery = 0x00,
};
//
static struct gatts_sample_data jar_sample_data;

// 该数组的元素顺序与枚举类型定义的顺序一致，请严格按顺序赋值
static const esp_gatts_attr_db_t gatt_db[HRS_IDX_NB] =
    {
        // 服务声明 - Service Declaration
        [IDX_SVC] =
            // 由协议栈自动回复,通常是在初始化时就固定的值,句柄由协议栈自动处理,无须手动处理
        {{ESP_GATT_AUTO_RSP},
         // BLE GATT中有主服务(Primary Service)和次服务(Secondary Service)两种类型:
         // - 主服务(0x2800): 独立的功能单元,可被客户端直接发现和使用,会在广播中暴露
         // - 次服务(0x2801): 辅助性服务,通常被主服务引用,不能独立存在,不在广播中主动暴露
         // 大多数应用使用主服务即可,复杂项目才需要设计次服务的层次结构
         //
         // 参数说明:
         // - primary_service_uuid: 服务类型标识符(0x2800,蓝牙联盟规定的主服务UUID)
         // - GATTS_SERVICE_UUID_SENSOR: 具体服务的UUID(0x00FF,开发者自定义的服务标识)
         // - 根据BLE GATT规范，服务声明属性必须是只读的，这是蓝牙联盟的强制要求
         {ESP_UUID_LEN_16, (uint8_t *)&primary_service_uuid, ESP_GATT_PERM_READ, sizeof(uint16_t), sizeof(GATTS_SERVICE_UUID_SENSOR), (uint8_t *)&GATTS_SERVICE_UUID_SENSOR}},
        //
        //
        //
        //
        //
        // 特征声明的作用：
        // - UUID固定为0x2803(蓝牙联盟规定的特征声明标识符)，告诉客户端"这里是一个特征声明"
        // - 包含特征属性信息(可读/可写/可通知等)，客户端通过读取此声明了解特征的能力
        // - 按GATT规范，特征声明后(按枚举类型定义的格式顺序排布)紧跟的下一个成员就是对应的特征值，无需额外关联,
        // - 大概起一个标志符的作用,像是告诉主机, 我是一个特征声明,下面紧挨着的就是特征值(如果顺序不错的话), 你不用去别的地方找了, 就是下面这个
        // 参数说明：
        // - char_prop_read_write_notify: 特征属性(支持读/写/通知)
        // 注意对比：服务声明的最后一个参数是服务UUID(自定义)，而特征声明的最后参数是特征属性
        // 原因：UUID决定了该ATT属性的"身份类型"，不同类型的属性需要存储不同格式的"值"
        // - 服务声明(0x2800) → 值为该服务的UUID
        // - 特征声明(0x2803) → 值为该特征的属性标志(读/写/通知等能力)
        // - 声明的权限是 权限主要是为了满足协议栈格式要求，几乎不用管业务

        // 设备信息 - 特征声明
        [IDX_CHAR_JAR_INFO] =
            {{ESP_GATT_AUTO_RSP},
             {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE, CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_only_read}},
        // 设备信息 - 特征值
        [IDX_CHAR_VAL_JAR_INFO] =
            {{ESP_GATT_AUTO_RSP}, // 值的权限才是 业务上实际控制读写
             {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_JAR_INFO, ESP_GATT_PERM_READ, GATTS_CHAR_VAL_LEN_MAX, sizeof(jar_info), (uint8_t *)&jar_info}},
        //
        //
        //
        //
        //
        // 采集数据 -> 特征声明 (支持读取和通知)
        [IDX_CHAR_JAR_SAMPLE_DATA] =
            {{ESP_GATT_AUTO_RSP},
             {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ, CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_read_notify}},

        // 采样数据 -> 特征值 (温度, 压力, 电量)
        [IDX_CHAR_VAL_JAR_SAMPLE_DATA] =
            {{ESP_GATT_RSP_BY_APP},
             // 因为上面的特征声明已经告诉你了(主机), 他是特征声明而下面紧接的成员(也就是我)就是特征值了
             // 而我,要通过UUID标识出来我是什么值(温度?重量?)而这些UUID都是主从事先约定好的(是开发者自定义的不是蓝牙联盟), 通信时通过此UUID就知道我代表什么数据了
             // GATTS_CHAR_VAL_LEN_MAX : 这个特征值最多能存储多少数据 最大长度：500字节
             {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_JAR_SAMPLE_DATA, ESP_GATT_PERM_READ, GATTS_CHAR_VAL_LEN_MAX, sizeof(jar_sample_data), (uint8_t *)&jar_sample_data}},

        // 采样数据 -> 客户端特征配置描述符 (CCCD) - 用于启用/禁用通知
        [IDX_CHAR_CFG_JAR_SAMPLE_DATA] =
            {{ESP_GATT_AUTO_RSP},
             {ESP_UUID_LEN_16, (uint8_t *)&character_client_config_uuid, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE, sizeof(uint16_t), sizeof(uint16_t), NULL}},
        //
        //
        //
        //
        //
        // 采样频率(读写)
        [IDX_CHAR_JAR_SAMPLE_RATE] =
            {{ESP_GATT_AUTO_RSP},
             {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ, CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_read_write}},
        //
        [IDX_CHAR_VAL_JAR_SAMPLE_RATE] =
            {{ESP_GATT_RSP_BY_APP},
             {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_JAR_SAMPLE_RATE, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE, GATTS_CHAR_VAL_LEN_MAX, sizeof(sample_rate), &sample_rate}},

};

// gatts_event_handler 通过判断gatts_if将事件及参数分派给对应的应用实例的事件处理函数(也就是这个函数)
void gatts_slave_profile_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    switch (event)
    {

    // 使能esp_ble_gatts_app_register()注册应用后,协议栈会触发该事件
    case ESP_GATTS_REG_EVT:
    {
        // 此时BLE协议栈已经完全初始化
        // GAP层功能已经可以使用
        // 设置设备名称的API才能正常工作
        // 太早设置 (比如在app_main中)：协议栈还没准备好，API调用可能失败
        // 太晚设置 (比如在连接事件中)：广播已经开始，设备名称已经对外暴露,再设置就晚了

        // 获取已缓存的设备固定信息
        esp_err_t err;
        err = get_device_id(&jar_info.device_id);
        err = get_device_mac(&jar_info.device_mac[0]);
        err = get_software_ver(&jar_info.soft_ver);
        jar_info.battery = 100;

        // 设置广播中的设备名称
        esp_err_t set_dev_name_ret = ble_gap_set_device_name(SAMPLE_DEVICE_NAME);
        if (set_dev_name_ret != ESP_OK)
        {
            ESP_LOGE(TAG, "set device name failed, error code = %x", set_dev_name_ret);
        }

        // 配置广播数据
        esp_err_t adv_config_ret = ble_gap_config_adv_data();
        if (adv_config_ret != ESP_OK)
        {
            ESP_LOGE(TAG, "config adv data failed, error code = %x", adv_config_ret);
        }

        // 将定义好属性表加载到协议栈,完成后会触发ESP_GATTS_CREAT_ATTR_TAB_EVT事件
        esp_err_t create_attr_ret = esp_ble_gatts_create_attr_tab(gatt_db, gatts_if, HRS_IDX_NB, SVC_INST_ID);
        if (create_attr_ret)
        {
            ESP_LOGE(TAG, "create attr table failed, error code = %x", create_attr_ret);
        }
    }
    break;
    case ESP_GATTS_READ_EVT:
        ESP_LOGI(TAG, "ESP_GATTS_READ_EVT, handle = %d ", param->read.handle);
        esp_gatt_rsp_t rsp;
        memset(&rsp, 0, sizeof(esp_gatt_rsp_t));

        // 设置响应的句柄，告诉协议栈这是对哪个属性读取请求的回复
        rsp.attr_value.handle = param->read.handle;

        // 主机读取采样数据
        if (slave_handle_table[IDX_CHAR_VAL_JAR_SAMPLE_DATA] == param->read.handle)
        {
            esp_err_t err = ESP_OK;

            // 获取当前配置的采样频率（已在写入时验证过范围）
            if (sample_rate == 1)
            {
                // 最出缓冲区的最新的1组元素
                //
            }
            else
            {
                // N>1: 从缓冲区获取最新的N组数据
                sensor_data_t buffer_data[GATTS_SAMPLE_RATE_MAX];
                int actual_count = data_collector_get_latest_data(buffer_data, sample_rate);

                // 复制数据到响应结构
                for (int i = 0; i < actual_count; i++)
                {
                    jar_sample_data.samples[i].temperature = buffer_data[i].temperature;
                    jar_sample_data.samples[i].pressure = buffer_data[i].pressure;
                }

                // 如果缓冲区数据不足，用0填充
                for (int i = actual_count; i < sample_rate; i++)
                {
                    jar_sample_data.samples[i].temperature = 0;
                    jar_sample_data.samples[i].pressure = 0;
                }

                // 更新实际返回的数据数量
                sample_rate = actual_count;
            }

            // 计算实际数据长度：固定部分 + 实际采样数据
            size_t data_size = (sample_rate * sizeof(sensor_data_t)); // 采样数据

            // 回复数据: 设备ID, 采样频率, 采样数据
            memcpy(&rsp.attr_value.value[0], &jar_info.device_id, sizeof(jar_info.device_id));
            memcpy(&rsp.attr_value.value[2], &sample_rate, sizeof(sample_rate));
            memcpy(&rsp.attr_value.value[3], &jar_sample_data, data_size);
            rsp.attr_value.len = sizeof(jar_info.device_id) + sizeof(sample_rate) + data_size;

            // 发送响应给主机
            esp_ble_gatts_send_response(gatts_if, param->read.conn_id, param->read.trans_id, ESP_GATT_OK, &rsp);
        }

        break;
    case ESP_GATTS_WRITE_EVT:
        ESP_LOGI(TAG, "ESP_GATTS_WRITE_EVT  handle = %d  write_len = %d", param->write.handle, param->write.len);

        // 处理CCCD写入 - 客户端启用/禁用通知
        if (slave_handle_table[IDX_CHAR_CFG_JAR_SAMPLE_DATA] == param->write.handle)
        {
            if (param->write.len == 2)
            {
                uint16_t descr_value = param->write.value[1] << 8 | param->write.value[0];
                if (descr_value == 0x0001)
                {
                    ESP_LOGI(TAG, "客户端启用通知");
                    notify_enable = true;
                    notify_conn_id = param->write.conn_id;
                    notify_gatts_if = gatts_if;
                }
                else if (descr_value == 0x0000)
                {
                    ESP_LOGI(TAG, "客户端禁用通知");
                    notify_enable = false;
                }
                else
                {
                    ESP_LOGE(TAG, "未知的描述符值: %04x", descr_value);
                }
            }

            // 发送写响应
            if (param->write.need_rsp)
            {
                esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, ESP_GATT_OK, NULL);
            }
        }
        else if (slave_handle_table[IDX_CHAR_VAL_JAR_SAMPLE_RATE] == param->write.handle)
        {
            // 主机写入配置参数(先检查数据合法性)
            if (param->write.value != NULL && param->write.len == sizeof(sample_rate))
            {
                uint8_t new_rate = *(param->write.value);
                if (new_rate > 0 && new_rate <= GATTS_SAMPLE_RATE_MAX)
                {
                    // 更新实际的采样频率设置
                    memcpy(&sample_rate, param->write.value, param->write.len);

                    // 根据采样频率启动(重启)数据采集器
                    esp_err_t ret = data_collector_start(sample_rate);
                    if (ret != ESP_OK)
                    {
                        ESP_LOGE(TAG, "启动定时采集失败: %s", esp_err_to_name(ret));
                    }
                }
            }
            else
            {
                ESP_LOGW(TAG, "配置数据长度错误: 期望 %d, 实际 %d", sizeof(sample_rate), param->write.len);
            }

            // 发送写响应
            if (param->write.need_rsp)
            {
                esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, ESP_GATT_OK, NULL);
            }
        }
        break;
    case ESP_GATTS_START_EVT:
        if (param->start.status != ESP_GATT_OK)
        {
            ESP_LOGE(TAG, "start service failed, error status = %x", param->start.status);
        }
        else
        {
            ESP_LOGI(TAG, "start service successfully, service_handle %d", param->start.service_handle);
            // 服务已启动，现在可以被主机发现和访问了
        }
        break;

    // GAP层建立连接后,无须手动处理,协议栈会自动触发该事件
    case ESP_GATTS_CONNECT_EVT:
        ESP_LOGI(TAG, "ESP_GATTS_CONNECT_EVT, conn_id = %d", param->connect.conn_id);

        // 打印远程设备的蓝牙地址(6字节MAC地址)
        esp_log_buffer_hex(TAG, param->connect.remote_bda, 6);

        // 重置通知状态
        notify_enable = false;
        notify_conn_id = param->connect.conn_id;
        notify_gatts_if = gatts_if;

        // 连接参数优化：主机建立连接时使用的默认参数可能不是最优的
        // 从机可以请求更新连接参数来优化性能和功耗
        esp_ble_conn_update_params_t conn_params = {0};
        memcpy(conn_params.bda, param->connect.remote_bda, sizeof(esp_bd_addr_t));

        // 设置优化后的连接参数
        // 对于iOS系统，请参阅苹果官方文件中关于BLE连接参数的限制
        // 注意：min_int 必须 <= max_int
        // 从机端优化参数
        conn_params.latency = 0;    // 改为0，确保立即响应
        conn_params.min_int = 0x25; // 0x18 * 1.25ms = 30ms
        conn_params.max_int = 0x30; // 0x30 * 1.25ms = 60ms
        conn_params.timeout = 600;  // 保持6秒超时

        // 向主机发送连接参数更新请求，主机可以接受或拒绝
        esp_ble_gap_update_conn_params(&conn_params);
        break;

    // 连接断开,重新恢复广播
    case ESP_GATTS_DISCONNECT_EVT:
        ESP_LOGI(TAG, "ESP_GATTS_DISCONNECT_EVT, reason = 0x%x", param->disconnect.reason);

        // 重置通知状态
        notify_enable = false;
        notify_conn_id = 0;
        notify_gatts_if = 0;

        ble_gap_start_advertising();
        break;

    // esp_ble_gatts_create_attr_tab()将定义好属性表加载到协议栈,完成后会触发该事件
    case ESP_GATTS_CREAT_ATTR_TAB_EVT:
    {
        // 检查属性表创建状态
        if (param->add_attr_tab.status != ESP_GATT_OK)
        {
            // 创建失败，打印错误码
            ESP_LOGE(TAG, "create attribute table failed, error code=0x%x", param->add_attr_tab.status);
        }
        else if (param->add_attr_tab.num_handle != HRS_IDX_NB)
        {
            // 创建的句柄数量与预期不符，可能是定义有问题
            ESP_LOGE(TAG, "create attribute table abnormally, num_handle (%d) \
                        doesn't equal to HRS_IDX_NB(%d)",
                     param->add_attr_tab.num_handle, HRS_IDX_NB);
        }
        else
        {
            // 属性表创建成功
            ESP_LOGI(TAG, "create attribute table successfully, the number handle = %d", param->add_attr_tab.num_handle);

            // 保存协议栈分配的句柄到全局数组，后续通过句柄来识别具体的属性
            // esp_ble_gatts_create_attr_tab()从机把属性表加载到协议栈后, 协议栈分配所有句柄,
            // 主机在通讯中会遍历所有服务, 服务下的特征声明, 特征值,  特征配置
            // 从机提供服务 → 协议栈分配句柄 → 主机自动发现 → 建立完整映射 - 后续通信完全基于句柄！
            memcpy(slave_handle_table, param->add_attr_tab.handles, sizeof(slave_handle_table));

            // 启动服务，使其对外可见和可访问
            // 使用服务的句柄(IDX_SVC对应的句柄)来启动整个服务
            esp_ble_gatts_start_service(slave_handle_table[IDX_SVC]);
        }
        break;
    }
    default:
        break;
    }
}
// event : 发生事件
// gatts_if : 协议栈分配的gatt_if, 用于识别是哪个APP发出的事件
// param : 事件参数, 不同事件携带不同参数, 具体参考 esp_ble_gatts_cb_param_t 结构体定义
// 所有的gatt事件先经过这里, 再通过gatts_if判断将该事件及参数分派给对应的应用实例
void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    // 应用注册成功后,协议栈会为每个应用分配一个gatts_if
    if (event == ESP_GATTS_REG_EVT)
    {
        if (param->reg.status == ESP_GATT_OK)
        {
            // heart_rate_profile_tabble[] 是一个 Profile(应用实例) 表，保存了每个服务的回调函数、gatts_if等信息
            // 开发者需要自己把gatt_if存储在 profile 表中，
            // 后续调用 BLE 协议栈中的大部分 GATT Server API，都需要用到对应的 gatts_if。
            // 这是 BLE 协议栈用来识别"你想操作哪个 GATT 应用"的唯一方式。
            gatt_app_profile_table[SLAVE_PROFILE_IDX].gatts_if = gatts_if;
        }
        else
        {
            ESP_LOGE(TAG, "reg app failed, app_id %04x, status %d", param->reg.app_id, param->reg.status);
            return;
        }
    }
    do
    {
        int idx;
        for (idx = 0; idx < PROFILE_NUM; idx++)
        {
            // 遍历gatt_app_profile_table中的所有服务,
            // 通过gatts_if来判断该事件是由哪个服务发出的,由该服务的回调函数来处理。
            // 如果gatts_if == ESP_GATT_IF_NONE, 说明事件不特定于某个应用，需要所有应用都处理。
            if (gatts_if == ESP_GATT_IF_NONE || gatts_if == gatt_app_profile_table[idx].gatts_if)
            {
                if (gatt_app_profile_table[idx].gatts_cb)
                {
                    gatt_app_profile_table[idx].gatts_cb(event, gatts_if, param);
                }
            }
        }
    } while (0);
}

// GATT 层初始化
esp_err_t ble_gatt_init(void)
{
    esp_err_t ret;

    // 注册 GATT 事件回调函数，BLE 核心事件（如连接、读写请求）必须通过这个回调接收处理
    ret = esp_ble_gatts_register_callback(gatts_event_handler);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "gatts register error, error code = %x", ret);
        return ret;
    }

    // 协议栈支持管理多个应用 → 应用管理多个服务 → 服务管理多个特征 → 应用的回调函数负责处理所有对应事件。
    // 每个应用注册自己的回调函数（gatts_cb），统一接收该应用所有服务、特征的事件
    // 每个应用由开发者指定一个 APP_ID, 通过调用该函数把应用注册到协议栈中
    // 注册成功后,事件回调函数 gatts_event_handler() 会捕获 ESP_GATTS_REG_EVT 事件
    // 协议栈会为每个应用分配一个gatt_if，开发者需要自己保存, 后续调用 BLE 协议栈中的大部分 GATT Server API，都需要用该 gatts_if
    ret = esp_ble_gatts_app_register(ESP_APP_ID);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "gatts app register error, error code = %x", ret);
        return ret;
    }

    // 设置本地最大 MTU（最大传输单元），提升 BLE 数据吞吐能力， 注意 4.2和5.0的最大MTU是不同的
    // 默认是 23，可调大，但最终大小由对端协商结果决定
    ret = esp_ble_gatt_set_local_mtu(200);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "set local  MTU failed, error code = %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "GATT initialized successfully");

    return ESP_OK;
}

// 发送通知函数
esp_err_t ble_gatt_send_notification(void)
{
    if (!notify_enable || notify_gatts_if == 0)
    {
        return ESP_ERR_INVALID_STATE; // 通知未启用或连接无效
    }

    // 获取最新的传感器数据
    sensor_data_t latest_data;
    int count = data_collector_get_latest_data(&latest_data, 1);
    if (count == 0)
    {
        ESP_LOGW(TAG, "没有可用的传感器数据");
        return ESP_ERR_NOT_FOUND;
    }

    // 准备通知数据：设备ID + 采样频率 + 传感器数据
    uint8_t notify_data[32];
    size_t notify_len = 0;

    // 设备ID (2字节)
    memcpy(&notify_data[notify_len], &jar_info.device_id, sizeof(jar_info.device_id));
    notify_len += sizeof(jar_info.device_id);

    // 采样频率 (1字节)
    uint8_t current_rate = 1; // 通知模式下固定为1Hz
    memcpy(&notify_data[notify_len], &current_rate, sizeof(current_rate));
    notify_len += sizeof(current_rate);

    // 传感器数据 (4字节: 2字节温度 + 2字节压力)
    memcpy(&notify_data[notify_len], &latest_data, sizeof(sensor_data_t));
    notify_len += sizeof(sensor_data_t);

    // 发送通知
    esp_err_t ret = esp_ble_gatts_send_indicate(notify_gatts_if, notify_conn_id,
                                                slave_handle_table[IDX_CHAR_VAL_JAR_SAMPLE_DATA],
                                                notify_len, notify_data, false);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "发送通知失败: %s", esp_err_to_name(ret));
    }
    else
    {
        ESP_LOGD(TAG, "通知发送成功，数据长度: %d", notify_len);
    }

    return ret;
}